#!/usr/bin/env swift

import Foundation
import Combine

// Simple test to verify UserDefaults publisher works
extension UserDefaults {
  var serverURL: String? {
    get { string(forKey: "serverURL") }
    set { set(newValue, forKey: "serverURL") }
  }
}

// Test the UserDefaults publisher
var cancellables = Set<AnyCancellable>()

print("Testing UserDefaults publisher for serverURL...")

UserDefaults.standard.publisher(for: \.serverURL)
  .compactMap { $0 }
  .removeDuplicates()
  .sink { newServerURL in
    print("📡 Server URL changed to: \(newServerURL)")
  }
  .store(in: &cancellables)

// Test changing the value
print("Setting initial value...")
UserDefaults.standard.serverURL = "http://localhost:8000"

print("Changing to production URL...")
UserDefaults.standard.serverURL = "https://app-cratenfc-testflight-westus.azurewebsites.net"

print("Setting same value again (should not trigger due to removeDuplicates)...")
UserDefaults.standard.serverURL = "https://app-cratenfc-testflight-westus.azurewebsites.net"

print("Changing back to localhost...")
UserDefaults.standard.serverURL = "http://localhost:8000"

// Give time for async operations
RunLoop.main.run(until: Date().addingTimeInterval(1))

print("Test completed!")

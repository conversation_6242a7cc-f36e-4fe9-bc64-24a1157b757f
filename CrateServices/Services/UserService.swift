import Foundation
import MSAL
import SwiftData

public protocol UserServiceProtocol {
  func login(email: String, password: String) async throws -> (UserDTO, String)
  func logout()
  func getMSALInstance() -> MSALNativeAuthPublicClientApplication?
}

// API Response Models
private struct AuthResponse: Codable {
  let email: String
  let username: String
  let token: String

  func toUser() -> User {
    return User(
      email: email,
      username: username,
      updatedAt: nil,
      createdAt: nil
    )
  }
}

private struct ProfileResponse: Codable {
  let id: String
  let email: String
  let username: String
  let lastLogin: String
  let entraSubjectId: String
  let created: String?
  let updated: String?

  enum CodingKeys: String, CodingKey {
    case id
    case email
    case username
    case lastLogin
    case entraSubjectId
    case created
    case updated
  }

  func toUser() -> User {
    // Create a DateFormatter for ISO 8601 dates
    let dateFormatter = ISO8601DateFormatter()
    // Parse the date strings into Date? objects
    let createdDate = created.flatMap { dateFormatter.date(from: $0) }
    let updatedDate = updated.flatMap { dateFormatter.date(from: $0) }

    return User(
      email: email,
      username: username,
      updatedAt: updatedDate,
      createdAt: createdDate
    )
  }

  func toUserDTO() -> UserDTO {
    // Create a DateFormatter for ISO 8601 dates
    let dateFormatter = ISO8601DateFormatter()
    // Parse the date strings into Date? objects
    let createdDate = created.flatMap { dateFormatter.date(from: $0) }
    let updatedDate = updated.flatMap { dateFormatter.date(from: $0) }

    return UserDTO(
      email: email,
      username: username,
      updatedAt: updatedDate,
      createdAt: createdDate
    )
  }
}

// Add this at the top of the file, after the imports
private struct EmptyParameters: Encodable {}

class Configuration: NSObject {
  // Update the below to your client ID and tenantSubdomain you received in the portal.

  static let clientId = "6d030d81-5d4a-42d1-a4c8-76c03b13fcc4"
  static let tenantSubdomain = "cratenfc"
  static let redirectURI = "msauth.com.lilrobo.CrateNFC://auth"
}

public class UserService: UserServiceProtocol {
  private let client: HTTPClientProtocol
  private let userState: UserState
  private let authPublisher = AuthPublisher.shared
  var nativeAuth: MSALNativeAuthPublicClientApplication!
  var accountResult: MSALNativeAuthUserAccountResult?

  // Continuation for login process
  private var loginContinuation: CheckedContinuation<(UserDTO, String), Error>?

  public init(client: HTTPClientProtocol = HTTPClient(), userState: UserState = UserState.shared) {
    self.client = client
    self.userState = userState

    do {
      nativeAuth = try MSALNativeAuthPublicClientApplication(
        clientId: Configuration.clientId,
        tenantSubdomain: Configuration.tenantSubdomain,
        challengeTypes: [.OOB, .password],
        redirectUri: Configuration.redirectURI
      )

      print("Initialized MSALNativeAuthPublicClientApplication")
    } catch {
      print("Unable to initialize MSAL \(error)")
    }

    tryRestoreLoginCachedMSALAccount()
  }

  // Add this method to expose the MSAL instance
  public func getMSALInstance() -> MSALNativeAuthPublicClientApplication? {
    return nativeAuth
  }

  // Request Models
  private struct LoginRequest: Codable {
    let email: String
    let password: String
  }

  func showResultText(_ text: String) {
    //        resultTextView.text = text
    print(text)
  }

  // API Methods
  public func login(email: String, password: String) async throws -> (UserDTO, String) {
    // To sign in a user using Email with password flow, we use the following code snippet:
    return try await withCheckedThrowingContinuation { continuation in
      // Store the continuation for later use in the delegate methods
      self.loginContinuation = continuation

      let parameters = MSALNativeAuthSignInParameters(username: email)
      parameters.password = password
      nativeAuth.signIn(parameters: parameters, delegate: self)
    }
  }

  public func logout() {
    print("Logging out user")
    // Remove the access token from the http client
    Task {
      await client.removeBearerToken()
    }
    // Change the UserState which triggers views to update
    userState.logout()

    guard let accountResult = nativeAuth.getNativeAuthUserAccount() else {
      print("🔒 No cached Native Auth user account found.")
      return
    }

    print("Found cached MSAL Native Auth Account from previous login")
    self.accountResult = accountResult

    self.accountResult?.signOut()
    self.accountResult = nil
    print("MSAL Account cleared from cache")
  }

  public func tryRestoreLoginCachedMSALAccount() {
    print("Starting tryRestoreLoginCachedMSALAccount automatic token refresh")
    guard let accountResult = nativeAuth.getNativeAuthUserAccount() else {
      print("🔒 No cached Native Auth user account found.")
      return
    }

    print("Found cached MSAL Native Auth Account from previous login")
    self.accountResult = accountResult

    let parameters = MSALNativeAuthGetAccessTokenParameters()
    parameters.scopes = ["api://cratenfc/access"]
    accountResult.getAccessToken(parameters: parameters, delegate: self)
  }
}

extension UserService: SignInStartDelegate {
  public func onSignInCompleted(result: MSAL.MSALNativeAuthUserAccountResult) {
    showResultText("sign in complete.")
    showResultText("Signed in: \(result.account.username ?? "")")
    accountResult = result

    let parameters = MSALNativeAuthGetAccessTokenParameters()
    parameters.scopes = ["api://cratenfc/access"]
    result.getAccessToken(parameters: parameters, delegate: self)

    // Note: We don't resume the continuation here because we need to wait for the access token
    // The continuation will be resumed in onAccessTokenRetrieveCompleted
  }

  public func onSignInStartError(error: MSAL.SignInStartError) {
    showResultText("SignInStartDelegate: onSignInStartError: \(error)")

    // Resume continuation with error
    if let continuation = loginContinuation {
      if error.isUserNotFound || error.isInvalidCredentials || error.isInvalidUsername {
        showResultText("Invalid username or password")
        continuation.resume(throwing: UserServiceError.loginFailed)
      } else {
        showResultText(
          "Error while signing in: \(error.errorDescription ?? "No error description")")
        continuation.resume(throwing: UserServiceError.loginFailed)
      }
      loginContinuation = nil
    }
  }
}

extension UserService: CredentialsDelegate {
  public func onAccessTokenRetrieveCompleted(result: MSALNativeAuthTokenResult) {
    print("Access Token: \(result.accessToken)")

    Task {
      await client.setBearerToken(result.accessToken)
    }

    Task {
      await fetchUserProfileAfterLogin(accessToken: result.accessToken)
    }

    showResultText(
      "Signed in." + "\n\n" + "Scopes:\n\(result.scopes)" + "\n\n"
        + "Access Token:\n\(result.accessToken)")
  }

  private func fetchUserProfileAfterLogin(accessToken: String) async {
    do {
      let profileResponse: ProfileResponse = try await client.get(
        path: "/api/v1/user/profile",
        parameters: nil as EmptyParameters?
      )
      print(profileResponse)

      let userDTO = profileResponse.toUserDTO()
      let user = userDTO.toModel()
      userState.login(user: user, token: accessToken)

      // Resume continuation with success
      if let continuation = self.loginContinuation {
        continuation.resume(returning: (userDTO, accessToken))
        self.loginContinuation = nil
      }
    } catch let httpError as HTTPError {
      handleProfileFetchError(httpError)
    } catch {
      handleUnexpectedProfileFetchError(error)
    }
  }

  private func handleProfileFetchError(_ httpError: HTTPError) {
    // Resume continuation with error if profile fetch fails
    if let continuation = self.loginContinuation {
      continuation.resume(throwing: UserServiceError.loginFailed)
      self.loginContinuation = nil
    }

    // Provide detailed error information based on the HTTP error type
    switch httpError {
    case .invalidResponse:
      showResultText("Error fetching profile: Invalid response from server")
    case let .unexpectedStatusCode(code):
      showResultText("Error fetching profile: Server returned status code \(code)")
    case .invalidURL:
      showResultText("Error fetching profile: Invalid URL for profile endpoint")
    case .encodingError:
      showResultText("Error fetching profile: Failed to encode request parameters")
    case let .decodingError(decodingError):
      showResultText(
        "Error fetching profile: Failed to decode server response\nDetails: \(decodingError.localizedDescription)"
      )
      // Print the full error for debugging
      print("Decoding error details: \(decodingError)")
    }
  }

  private func handleUnexpectedProfileFetchError(_ error: Error) {
    // Handle any other non-HTTP errors
    if let continuation = self.loginContinuation {
      continuation.resume(throwing: UserServiceError.loginFailed)
      self.loginContinuation = nil
    }

    showResultText("Error fetching profile: \(error.localizedDescription)")
    print("Unexpected error type: \(type(of: error))")
    print("Error details: \(error)")
  }

  public func onAccessTokenRetrieveError(error: MSAL.RetrieveAccessTokenError) {
    showResultText(
      "Error retrieving access token: \(error.errorDescription ?? "No error description")")

    // Resume continuation with error
    if let continuation = loginContinuation {
      continuation.resume(throwing: UserServiceError.loginFailed)
      loginContinuation = nil
    }
  }
}

// Errors
public enum UserServiceError: Error {
  case notAuthenticated
  case loginFailed
  case emailAlreadyExists
  case registrationFailed
}
